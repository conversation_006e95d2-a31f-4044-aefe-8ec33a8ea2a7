<script setup>
import {
  Icon<PERSON>rrowRight,
  IconBrandFacebook,
  IconBrandInstagram,
  IconPills,
  IconTruckDelivery,
} from "@tabler/icons-vue";
import { onMounted, onUnmounted, ref } from "vue";

const isMenuOpen = ref(false);
const activeMobileDropdown = ref(null);
const activeDropdown = ref(null);
const phrases = ["Losing weight.", "Better sex.", "Fuller hair."];

const currentPhrase = ref(phrases[0]);

const toggleDropdown = (menu) => {
  if (activeDropdown.value === menu) {
    activeDropdown.value = null;
  } else {
    activeDropdown.value = menu;
  }
};

const closeDropdown = () => {
  activeDropdown.value = null;
};

const toggleMenu = () => {
  isMenuOpen.value = !isMenuOpen.value;
  if (!isMenuOpen.value) {
    activeMobileDropdown.value = null;
  }
};

const toggleMobileDropdown = (menu) => {
  if (activeMobileDropdown.value === menu) {
    activeMobileDropdown.value = null;
  } else {
    activeMobileDropdown.value = menu;
  }
};

let index = 0;
let intervalId;

const features = [
  {
    title: "Fast Delivery",
    description:
      "Get your medications delivered quickly and safely to your doorstep with our expedited shipping options.",
    icon: "M13 10V3L4 14h7v7l9-11h-7z",
  },
  {
    title: "Quality Assured",
    description:
      "All products are FDA approved and sourced from certified manufacturers with rigorous quality control.",
    icon: "M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z",
  },
  {
    title: "24/7 Support",
    description:
      "Our dedicated customer service team is available around the clock to assist with your healthcare needs.",
    icon: "M18.364 5.636l-3.536 3.536m0 5.656l3.536 3.536M9.172 9.172L5.636 5.636m3.536 9.192L5.636 18.364M12 2.25a9.75 9.75 0 100 19.5 9.75 9.75 0 000-19.5z",
  },
  {
    title: "Secure Payment",
    description:
      "Your payment information is protected with bank-level security and encrypted transactions.",
    icon: "M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z",
  },
  {
    title: "Licensed Pharmacy",
    description:
      "We are a fully licensed and regulated online pharmacy operating under strict healthcare standards.",
    icon: "M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z",
  },
  {
    title: "Expert Consultation",
    description:
      "Get professional advice and personalized recommendations from our qualified healthcare professionals.",
    icon: "M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z",
  },
];

import brandedViagra from '@/assets/images/products/branded-viagra.png';
import brandedCialis from '@/assets/images/products/branded-cialis.png';
import finasteride from '@/assets/images/products/finasteride.png';
import minoxidil from '@/assets/images/products/minoxidil.png';
import semaglutide from '@/assets/images/products/semaglutide.png';
import tirzepatide from '@/assets/images/products/tirzepatide.png';

const products = [
  {
    name: "Sildenafil (Viagra)",
    description: "Effective oral medication for treating erectile dysfunction.",
    category: "Sexual Health",
    stock: true,
    image: brandedViagra,
  },
  {
    name: "Tadalafil (Cialis)",
    description: "Long-lasting treatment for erectile dysfunction and BPH.",
    category: "Sexual Health",
    stock: true,
    image: brandedCialis,
  },
  {
    name: "Finasteride",
    description: "Prescription oral medication for male pattern hair loss.",
    category: "Hair Health",
    stock: true,
    image: finasteride,
  },
  {
    name: "Minoxidil",
    description: "Topical solution to stimulate hair regrowth and slow balding.",
    category: "Hair Health",
    stock: false,
    image: minoxidil,
  },
  {
    name: "Semaglutide",
    description: "GLP-1 medication to support significant weight loss.",
    category: "Weight Loss",
    stock: true,
    image: semaglutide,
  },
  {
    name: "Tirzepatide",
    description: "Dual-action injectable for advanced weight management.",
    category: "Weight Loss",
    stock: false,
    image: tirzepatide,
  },
];

const testimonials = [
  {
    name: "Sarah Johnson",
    location: "New York, NY",
    review:
      "Outstanding service and product quality. The delivery was incredibly fast and the customer support team went above and beyond to help me find the right supplements.",
  },
  {
    name: "Michael Chen",
    location: "Los Angeles, CA",
    review:
      "I have been a loyal customer for over two years. The products are authentic, reasonably priced, and the website makes ordering so convenient.",
  },
  {
    name: "Emily Davis",
    location: "Chicago, IL",
    review:
      "Exceptional experience from start to finish. The user-friendly interface and seamless checkout process make this my go-to healthcare provider.",
  },
];

const faqs = ref([
  {
    question: "How does ConnectRx work?",
    answer:
      "ConnectRx is a telehealth platform that connects you with US-licensed physicians for a variety of treatments. Simply complete an online visit, and a physician will review your information and, if appropriate, write a prescription. Your medication will then be shipped to your door in discreet packaging.",
    open: false,
  },
  {
    question: "Is ConnectRx legit?",
    answer:
      "Yes, ConnectRx is a legitimate telehealth platform. We only work with US-licensed physicians and FDA-regulated pharmacies to ensure you receive the highest quality care and medication.",
    open: false,
  },
  {
    question: "What treatments does ConnectRx offer?",
    answer:
      "ConnectRx offers a variety of treatments for weight loss, sexual health, daily wellness, and hair loss. You can explore all of our treatments on our website.",
    open: false,
  },
  {
    question: "How much does ConnectRx cost?",
    answer:
      "The cost of ConnectRx varies depending on the treatment you choose. We offer transparent pricing with no hidden fees. You can find the price of each treatment on our website.",
    open: false,
  },
  {
    question: "Do I need a prescription?",
    answer:
      "Yes, all of our treatments require a prescription from a US-licensed physician. You can get a prescription by completing an online visit with one of our physicians.",
    open: false,
  },
]);

const toggle = (index) => {
  faqs.value = faqs.value.map((faq, i) => {
    if (i === index) {
      faq.open = !faq.open;
    } else {
      faq.open = false;
    }
    return faq;
  });
};

onMounted(() => {
  intervalId = setInterval(() => {
    index = (index + 1) % phrases.length;
    currentPhrase.value = phrases[index];
  }, 2500); // change every 2.5 seconds
});

onUnmounted(() => {
  clearInterval(intervalId);
});
</script>

<template>
  <div class="">
    <!-- navbar -->
    <nav
      class="sticky top-0 z-50 bg-gradient-to-r from-white/95 via-gray-50/95 to-white/95 backdrop-blur-sm border-b border-gray-200/30">
      <!-- Top Banner -->
      <div class="bg-gradient-to-r from-gray-100 via-stone-100 to-gray-100 border-b border-gray-200/50">
        <div class="max-w-7xl mx-auto px-4 py-2">
          <div class="flex items-center justify-center space-x-8">
            <div class="flex items-center space-x-2 whitespace-nowrap">
              <div class="w-4 h-4 -mt-2">🇺🇸</div>
              <span class="text-xs">US sourced ingredients</span>
            </div>
            <div class="flex items-center space-x-2 whitespace-nowrap">
              <div class="w-4 h-4 -mt-2">⭐</div>
              <span class="text-xs">Trusted by over 100K subscribers</span>
            </div>
            <div class="flex items-center space-x-2 whitespace-nowrap">
              <div class="w-4 h-4 -mt-2">💻</div>
              <span class="text-xs">100% online process</span>
            </div>
            <div class="flex items-center space-x-2 whitespace-nowrap">
              <div class="w-4 h-4 -mt-2">👤</div>
              <span class="text-xs">No membership requirements</span>
            </div>
          </div>
        </div>
      </div>

      <!-- Main Navigation -->
      <div class="max-w-7xl mx-auto px-4">
        <div class="flex justify-between items-center py-4">
          <!-- Logo -->
          <a href="#" class="flex-shrink-0">
            <div class="text-2xl font-bold text-gray-800">ConnectRx</div>
          </a>

          <!-- Desktop Menu -->
          <div class="hidden lg:flex items-center space-x-8">
            <!-- Weight Loss Dropdown -->
            <div class="relative">
              <button @click="toggleDropdown('weight-loss')"
                class="flex items-center px-3 py-2 text-gray-700 hover:text-gray-900 transition-colors cursor-pointer hover:border-b-2 border-gray-900"
                :class="{ 'border-b-2 border-gray-900': activeDropdown === 'weight-loss' }">
                <span>Weight Loss</span>
              </button>
            </div>

            <!-- Sexual Health Dropdown -->
            <div class="relative">
              <button @click="toggleDropdown('sexual-health')"
                class="flex items-center px-3 py-2 text-gray-700 hover:text-gray-900 transition-colors cursor-pointer hover:border-b-2 border-gray-900"
                :class="{ 'border-b-2 border-gray-900': activeDropdown === 'sexual-health' }">
                <span>Sexual Health</span>
              </button>
            </div>

            <!-- Hair Dropdown -->
            <div class="relative">
              <button @click="toggleDropdown('hair')"
                class="flex items-center px-3 py-2 text-gray-700 hover:text-gray-900 transition-colors cursor-pointer hover:border-b-2 border-gray-900"
                :class="{ 'border-b-2 border-gray-900': activeDropdown === 'hair' }">
                <span>Hair</span>
              </button>
            </div>

            <!-- Top Products Dropdown -->
            <div class="relative">
              <button @click="toggleDropdown('top-products')"
                class="flex items-center px-3 py-2 text-gray-700 hover:text-gray-900 transition-colors cursor-pointer hover:border-b-2 border-gray-900"
                :class="{ 'border-b-2 border-gray-900': activeDropdown === 'top-products' }">
                <span>Top Products</span>
              </button>
            </div>
          </div>

          <!-- CTA Buttons -->
          <div class="hidden lg:flex items-center space-x-4">
            <!-- <a href="#" class="px-6 py-2 bg-black text-white rounded-full hover:bg-gray-800 hover:scale-105 transition-all">
                Get Started
              </a> -->
            <a href="#"
              class="px-6 py-2 border border-gray-300 text-gray-700 rounded-full hover:bg-gray-50 transition-colors">
              Log In
            </a>
          </div>

          <!-- Mobile Menu Button -->
          <button @click="toggleMenu" class="lg:hidden p-2">
            <div class="w-6 h-6 flex flex-col justify-center items-center space-y-1">
              <div class="w-5 h-0.5 bg-gray-600 transition-all" :class="{
                'rotate-45 translate-y-1.5': isMenuOpen,
              }"></div>
              <div class="w-5 h-0.5 bg-gray-600 transition-all" :class="{ 'opacity-0': isMenuOpen }"></div>
              <div class="w-5 h-0.5 bg-gray-600 transition-all" :class="{
                '-rotate-45 -translate-y-1.5': isMenuOpen,
              }"></div>
            </div>
          </button>
        </div>

        <!-- Mobile Menu -->
        <transition name="slide-down">
          <div v-if="isMenuOpen" class="lg:hidden inset-0 z-50 flex flex-col bg-white">
            <!-- Menu content scrolls -->
            <div class="flex-1 overflow-y-auto px-4 pt-6 pb-10">
              <div class="flex flex-col gap-6">
                <button @click="toggleMobileDropdown('weight-loss')"
                  class="flex items-center justify-between w-full py-3 text-lg font-medium text-gray-900 border-b border-gray-100"
                  :class="{ 'border-b-2 border-gray-900': activeMobileDropdown === 'weight-loss' }">
                  <span>Weight Loss</span>
                  <svg class="w-5 h-5 transition-transform"
                    :class="{ 'rotate-180': activeMobileDropdown === 'weight-loss' }" fill="none" stroke="currentColor"
                    viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                  </svg>
                </button>
                <div v-show="activeMobileDropdown === 'weight-loss'" class="pl-4 mt-2 space-y-2">
                  <div>
                    <div class="text-xs text-gray-500 mb-1">Treatments</div>
                    <a href="#" class="block text-gray-800 py-1">Compounded Semaglutide <span
                        class="align-super text-xs">Rx</span></a>
                    <a href="#" class="block text-gray-800 py-1">Compounded Tirzepatide <span
                        class="align-super text-xs">Rx</span></a>
                  </div>
                  <div>
                    <div class="text-xs text-gray-500 mb-1">Name Brand</div>
                    <a href="#" class="block text-gray-800 py-1">Wegovy <span class="align-super text-xs">Rx</span></a>
                    <a href="#" class="block text-gray-800 py-1">Ozempic <span class="align-super text-xs">Rx</span></a>
                  </div>
                  <div class="pt-2">
                    <img src="@/assets/images/wl-kit.png" alt="Weight Loss Products"
                      class="w-full h-32 object-contain rounded-lg bg-gray-50" />
                  </div>
                </div>
                <button @click="toggleMobileDropdown('sexual-health')"
                  class="flex items-center justify-between w-full py-3 text-lg font-medium text-gray-900 border-b border-gray-100"
                  :class="{ 'border-b-2 border-gray-900': activeMobileDropdown === 'sexual-health' }">
                  <span>Sexual Health</span>
                  <svg class="w-5 h-5 transition-transform"
                    :class="{ 'rotate-180': activeMobileDropdown === 'sexual-health' }" fill="none"
                    stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                  </svg>
                </button>
                <div v-show="activeMobileDropdown === 'sexual-health'" class="pl-4 mt-2 space-y-2">
                  <div>
                    <div class="text-xs text-gray-500 mb-1">Treatments</div>
                    <a href="#" class="block text-gray-800 py-1">Tadalafil (Generic Cialis) <span
                        class="align-super text-xs">Rx</span></a>
                    <a href="#" class="block text-gray-800 py-1">Sildenafil (Generic Viagra) <span
                        class="align-super text-xs">Rx</span></a>
                  </div>
                  <div class="pt-2">
                    <img src="@/assets/images/ed-pills.png" alt="Sexual Health Products"
                      class="w-full h-32 object-contain rounded-lg bg-gray-50" />
                  </div>
                </div>
                <button @click="toggleMobileDropdown('hair')"
                  class="flex items-center justify-between w-full py-3 text-lg font-medium text-gray-900 border-b border-gray-100"
                  :class="{ 'border-b-2 border-gray-900': activeMobileDropdown === 'hair' }">
                  <span>Hair</span>
                  <svg class="w-5 h-5 transition-transform" :class="{ 'rotate-180': activeMobileDropdown === 'hair' }"
                    fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                  </svg>
                </button>
                <div v-show="activeMobileDropdown === 'hair'" class="pl-4 mt-2 space-y-2">
                  <div>
                    <div class="text-xs text-gray-500 mb-1">Treatments</div>
                    <a href="#" class="block text-gray-800 py-1">Finasteride <span
                        class="align-super text-xs">Rx</span></a>
                    <a href="#" class="block text-gray-800 py-1">Minoxidil</a>
                  </div>
                  <div class="pt-2">
                    <img src="@/assets/images/hl-pills.png" alt="Hair Products"
                      class="w-full h-32 object-contain rounded-lg bg-gray-50" />
                  </div>
                </div>
                <button @click="toggleMobileDropdown('top-products')"
                  class="flex items-center justify-between w-full py-3 text-lg font-medium text-gray-900 border-b border-gray-100"
                  :class="{ 'border-b-2 border-gray-900': activeMobileDropdown === 'top-products' }">
                  <span>Top Products</span>
                  <svg class="w-5 h-5 transition-transform"
                    :class="{ 'rotate-180': activeMobileDropdown === 'top-products' }" fill="none" stroke="currentColor"
                    viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                  </svg>
                </button>
                <div v-show="activeMobileDropdown === 'top-products'" class="pl-4 mt-2 space-y-2">
                  <div>
                    <div class="text-xs text-gray-500 mb-1">Featured Products</div>
                    <a href="#" class="block text-gray-800 py-1">Semaglutide <span
                        class="align-super text-xs">Rx</span></a>
                    <a href="#" class="block text-gray-800 py-1">Tadalafil <span
                        class="align-super text-xs">Rx</span></a>
                    <a href="#" class="block text-gray-800 py-1">Finasteride <span
                        class="align-super text-xs">Rx</span></a>
                  </div>
                </div>
                <a href="#"
                  class="block w-full mt-6 py-3 text-center text-gray-900 border-2 border-gray-900 rounded-lg hover:bg-gray-900 hover:text-white transition-colors">Log
                  In</a>
              </div>
            </div>
          </div>
        </transition>
      </div>
    </nav>

    <!-- Mega Menu Dropdowns -->
    <div v-if="activeDropdown" @click="closeDropdown" class="fixed inset-0 bg-black/10 backdrop-blur-[2px] z-40"></div>

    <!-- Weight Loss Dropdown -->
    <div v-show="activeDropdown === 'weight-loss'"
      class="fixed left-0 right-0 top-[106px] bg-white/95 shadow-lg z-40 backdrop-blur-sm">
      <div class="max-w-7xl mx-auto px-8 py-8 grid grid-cols-4 gap-8">
        <div>
          <h3 class="text-sm font-semibold text-gray-600 mb-5">Discover</h3>
          <ul class="space-y-4">
            <li><a href="#" class="text-lg text-gray-800 hover:underline transition-all">Weight Loss</a></li>
          </ul>
        </div>
        <div>
          <h3 class="text-sm font-semibold text-gray-600 mb-5">Treatments</h3>
          <ul class="space-y-4">
            <li>
              <a href="#" class="text-sm text-gray-800 hover:underline transition-all">Compounded Semaglutide <span
                  class="align-super text-xs">Rx</span></a>
            </li>
            <li>
              <a href="#" class="text-sm text-gray-800 hover:underline transition-all">Compounded Tirzepatide <span
                  class="align-super text-xs">Rx</span></a>
            </li>
          </ul>
        </div>
        <div>
          <h3 class="text-sm font-semibold text-gray-600 mb-5">Name Brand</h3>
          <ul class="space-y-4">
            <li>
              <a href="#" class="text-sm text-gray-800 hover:underline transition-all">Wegovy <span
                  class="align-super text-xs">Rx</span></a>
            </li>
            <li>
              <a href="#" class="text-sm text-gray-800 hover:underline transition-all">Ozempic <span
                  class="align-super text-xs">Rx</span></a>
            </li>
          </ul>
        </div>
        <div>
          <h3 class="text-sm font-semibold text-gray-600 mb-5">Best Selling</h3>
          <div>
            <img src="@/assets/images/wl-kit.png" alt="Weight Loss Products"
              class="w-full h-48 object-contain rounded-lg bg-gray-50" />
          </div>
        </div>
      </div>
    </div>

    <!-- Sexual Health Dropdown -->
    <div v-show="activeDropdown === 'sexual-health'"
      class="fixed left-0 right-0 top-[106px] bg-white/95 shadow-lg z-40 backdrop-blur-sm">
      <div class="max-w-7xl mx-auto px-8 py-8 grid grid-cols-4 gap-8">
        <div>
          <h3 class="text-sm font-semibold text-gray-600 mb-5">Discover</h3>
          <ul class="space-y-4">
            <li><a href="#" class="text-lg text-gray-800 hover:underline transition-all">Sexual Health</a></li>
          </ul>
        </div>
        <div>
          <h3 class="text-sm font-semibold text-gray-600 mb-5">Treatments</h3>
          <ul class="space-y-4">
            <li>
              <a href="#" class="text-sm text-gray-800 hover:underline transition-all">Tadalafil (Generic Cialis) <span
                  class="align-super text-xs">Rx</span></a>
            </li>
            <li>
              <a href="#" class="text-sm text-gray-800 hover:underline transition-all">Sildenafil (Generic Viagra) <span
                  class="align-super text-xs">Rx</span></a>
            </li>
          </ul>
        </div>
        <div>
          <h3 class="text-sm font-semibold text-gray-600 mb-5">Name Brand</h3>
          <ul class="space-y-4">
            <li>
              <a href="#" class="text-sm text-gray-800 hover:underline transition-all">Viagra <span
                  class="align-super text-xs">Rx</span></a>
            </li>
            <li>
              <a href="#" class="text-sm text-gray-800 hover:underline transition-all">Cialis <span
                  class="align-super text-xs">Rx</span></a>
            </li>
          </ul>
        </div>
        <div>
          <h3 class="text-sm font-semibold text-gray-600 mb-5">Best Selling</h3>
          <div>
            <img src="@/assets/images/ed-pills.png" alt="Weight Loss Products"
              class="w-full h-48 object-contain rounded-lg bg-gray-50" />
          </div>
        </div>
      </div>
    </div>

    <!-- Hair Dropdown -->
    <div v-show="activeDropdown === 'hair'"
      class="fixed left-0 right-0 top-[106px] bg-white/95 shadow-lg z-40 backdrop-blur-sm">
      <div class="max-w-7xl mx-auto px-8 py-8 grid grid-cols-4 gap-8">
        <div>
          <h3 class="text-sm font-semibold text-gray-600 mb-5">Discover</h3>
          <ul class="space-y-4">
            <li><a href="#" class="text-lg text-gray-800 hover:underline transition-all">Hair</a></li>
          </ul>
        </div>
        <div class="col-span-2">
          <h3 class="text-sm font-semibold text-gray-600 mb-5">Treatments</h3>
          <ul class="space-y-4">
            <li>
              <a href="#" class="text-sm text-gray-800 hover:underline transition-all">Oral Finasteride <span
                  class="align-super text-xs">Rx</span></a>
            </li>
            <li>
              <a href="#" class="text-sm text-gray-800 hover:underline transition-all">Oral Minoxidil <span
                  class="align-super text-xs">Rx</span></a>
            </li>
          </ul>
        </div>
        <div>
          <h3 class="text-sm font-semibold text-gray-600 mb-5">Best Selling</h3>
          <div>
            <img src="@/assets/images/hl-pills.png" alt="Weight Loss Products"
              class="w-full h-48 object-contain rounded-lg bg-gray-50" />
          </div>
        </div>
      </div>
    </div>

    <!-- Top Products Dropdown -->
    <div v-show="activeDropdown === 'top-products'"
      class="fixed left-0 right-0 top-[106px] bg-white/95 shadow-lg z-40 backdrop-blur-sm">
      <div class="max-w-7xl mx-auto px-8 py-8 grid grid-cols-3 gap-8">
        <div>
          <h3 class="text-sm font-semibold text-gray-600 mb-5">Weight Loss</h3>
          <ul class="space-y-4">
            <li>
              <a href="#" class="text-sm text-gray-800 hover:underline transition-all">Compounded Semaglutide <span
                  class="align-super text-xs">Rx</span></a>
            </li>
            <li>
              <a href="#" class="text-sm text-gray-800 hover:underline transition-all">Compounded Tirzepatide <span
                  class="align-super text-xs">Rx</span></a>
            </li>
          </ul>
        </div>
        <div>
          <h3 class="text-sm font-semibold text-gray-600 mb-5">Sexual Health</h3>
          <ul class="space-y-4">
            <li>
              <a href="#" class="text-sm text-gray-800 hover:underline transition-all">Tadalafil (Generic Cialis) <span
                  class="align-super text-xs">Rx</span></a>
            </li>
            <li>
              <a href="#" class="text-sm text-gray-800 hover:underline transition-all">Sildenafil (Generic Viagra) <span
                  class="align-super text-xs">Rx</span></a>
            </li>
          </ul>
        </div>
        <div>
          <h3 class="text-sm font-semibold text-gray-600 mb-5">Hair</h3>
          <ul class="space-y-4">
            <li>
              <a href="#" class="text-sm text-gray-800 hover:underline transition-all">Oral Finasteride <span
                  class="align-super text-xs">Rx</span></a>
            </li>
            <li>
              <a href="#" class="text-sm text-gray-800 hover:underline transition-all">Oral Minoxidil <span
                  class="align-super text-xs">Rx</span></a>
            </li>
          </ul>
        </div>
      </div>
    </div>

    <!-- Hero -->
    <section class="relative py-12 bg-white sm:py-16 lg:py-20 overflow-hidden">
      <!-- Animated background accent -->
      <div
        class="absolute -top-40 left-1/2 -translate-x-1/2 w-[700px] h-[700px] bg-amber-200/30 rounded-full blur-3xl animate-pulse-slow z-0">
      </div>
      <div class="absolute inset-0 z-0">
        <img class="object-cover w-full h-full" src="@/assets/images/grid-pattern.png" alt="" />
      </div>

      <!-- Hero content -->
      <div class="relative px-4 mx-auto sm:px-6 lg:px-8 max-w-7xl z-10">
        <div class="max-w-5xl mx-auto text-center px-4">
          <h1 class="text-3xl font-medium text-gray-900 sm:text-5xl lg:text-7xl">
            <Transition name="slide-up" mode="out-in">
              <span :key="currentPhrase" class="text-amber-500 font-semibold block py-2">
                {{ currentPhrase }}
              </span>
            </Transition>
            <span class="ml-2">We've got you covered.</span>
          </h1>
          <p class="max-w-md mx-auto mt-6 text-base font-normal leading-7 text-gray-500">
            Personalized, high-quality treatments shipped directly to your doorstep.
          </p>

          <ul class="flex flex-col gap-2 sm:flex-row items-center justify-center mt-6 space-x-6 sm:space-x-8">
            <li class="flex items-center">
              <IconTruckDelivery class="w-5 h-5 mr-2 text-gray-400" />
              <span class="text-xs font-medium text-gray-900 sm:text-sm">
                Free shipping, if prescribed
              </span>
            </li>
            <li class="flex items-center">
              <IconPills class="w-5 h-5 mr-2 text-gray-400" />
              <span class="text-xs font-medium text-gray-900 sm:text-sm">
                Lab tested for quality and safety
              </span>
            </li>
          </ul>
        </div>
      </div>

      <!-- Treatment cards -->
      <div class="max-w-7xl mx-auto grid grid-cols-1 lg:grid-cols-2 gap-5 pb-8 mt-12 sm:mt-16 lg:mt-20 px-4">
        <!-- Weight Loss Card -->
        <a href="#"
          class="relative rounded-2xl min-h-[350px] sm:min-h-[400px] flex flex-col p-8 bg-gradient-to-br from-[#fef6e7] via-[#fdf3e6] to-[#fbeeea] overflow-hidden group hover:shadow-md transition-all duration-300 border border-amber-100">
          <h3 class="text-amber-700 text-2xl font-bold mb-4">Weight Loss</h3>
          <p class="text-amber-800 text-base mb-4 max-w-md">
            Lose up to 15% of your body weight with cutting-edge treatments.
          </p>
          <div class="relative flex-1">
            <img src="@/assets/images/wl-kit.png" alt="Weight Loss"
              class="w-32 h-32 object-contain select-none pointer-events-none absolute -left-2 bottom-6 lg:w-52 lg:h-52 lg:left-8 lg:bottom-8 group-hover:scale-110 transition-transform duration-300" />
          </div>
          <div
            class="absolute right-8 bottom-8 bg-black text-white font-semibold px-5 py-2.5 rounded-full shadow hover:bg-gray-800 transition-all flex items-center z-10">
            Get Started
            <IconArrowRight class="inline-block w-4 h-4 ml-1 group-hover:translate-x-1 transition-all" />
          </div>
        </a>
        <div class="flex flex-col gap-5 h-full">
          <!-- Sexual Health Card -->
          <a href="#"
            class="relative rounded-2xl p-8 min-h-[250px] flex flex-col bg-gradient-to-br from-[#e7eafc] via-[#f0f3fd] to-[#eaf3fb] overflow-hidden group hover:shadow-md transition-all duration-300 border border-blue-100">
            <h3 class="text-blue-800 text-2xl font-bold mb-2">Sexual health</h3>
            <p class="text-blue-900 text-base mb-2">
              Regain your confidence and have the sexual life you’ve always wanted.
            </p>
            <div class="relative flex-1">
              <img src="@/assets/images/ed-pills.png" alt="Sexual Health"
                class="w-20 h-20 object-contain select-none pointer-events-none absolute left-0 bottom-0 lg:w-24 lg:h-24 lg:left-2 lg:-bottom-5 group-hover:scale-110 transition-transform duration-300" />
            </div>
            <div
              class="absolute right-8 bottom-8 bg-black text-white font-semibold px-5 py-2 rounded-full shadow hover:bg-gray-800 transition-all flex items-center z-10">
              Get Started
              <IconArrowRight class="inline-block w-4 h-4 ml-1 group-hover:translate-x-1 transition-all" />
            </div>
          </a>
          <!-- Hair Health Card -->
          <a href="#"
            class="relative rounded-2xl p-8 min-h-[250px] sm:min-h-[220px] flex flex-col bg-gradient-to-br from-[#e7fcf3] via-[#e6fdf3] to-[#eafbf7] overflow-hidden group hover:shadow-md transition-all duration-300 border border-green-100">
            <h3 class="text-green-800 text-2xl font-bold mb-2">Hair Health</h3>
            <p class="text-green-900 text-base mb-2">
              Address hair loss at the root with prescription medication.
            </p>
            <div class="relative flex-1">
              <img src="@/assets/images/hl-pills.png" alt="Hair Health"
                class="w-24 h-24 object-contain select-none pointer-events-none absolute -left-2 -bottom-2 lg:w-28 lg:h-28 lg:left-2 lg:-bottom-5 group-hover:scale-110 transition-transform duration-300" />
            </div>
            <div
              class="absolute right-8 bottom-8 bg-black text-white font-semibold px-5 py-2 rounded-full shadow hover:bg-gray-800 transition-all flex items-center z-10">
              Get Started
              <IconArrowRight class="inline-block w-4 h-4 ml-1 group-hover:translate-x-1 transition-all" />
            </div>
          </a>
        </div>
      </div>
    </section>

    <!-- Features Section -->
    <section class="pb-24 pt-10 bg-white">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-20">
          <h2 class="text-4xl lg:text-5xl font-bold text-neutral-900 mb-6">
            Why Choose Us
          </h2>
          <p class="text-xl text-neutral-600 max-w-3xl mx-auto">
            We're committed to delivering exceptional healthcare solutions with
            modern convenience and trusted quality.
          </p>
        </div>

        <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
          <div v-for="feature in features" :key="feature.title"
            class="group p-8 rounded-2xl hover:bg-amber-50 transition-all duration-300 border border-gray-100 hover:border-amber-200">
            <div
              class="w-14 h-14 bg-neutral-100 rounded-2xl flex items-center justify-center mb-6 group-hover:bg-amber-200 transition-colors">
              <svg class="w-7 h-7 text-neutral-700" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" :d="feature.icon"></path>
              </svg>
            </div>
            <h3 class="text-xl font-semibold text-neutral-900 mb-4">
              {{ feature.title }}
            </h3>
            <p class="text-neutral-600 leading-relaxed">
              {{ feature.description }}
            </p>
          </div>
        </div>
      </div>
    </section>

    <!-- Product Highlights -->
    <section class="py-24 bg-neutral-50">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="w-full flex justify-center items-center mb-20">
          <h2 class="text-4xl lg:text-5xl font-bold text-neutral-900 mb-6 flex-1">
            Top products
          </h2>
          <a href="#"
            class="bg-neutral-900 text-white px-6 py-2 rounded-full hover:bg-neutral-800 transition-all duration-200 font-medium cursor-pointer">Explore
            All Treatments</a>
        </div>

        <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
          <div v-for="product in products" :key="product.name"
            class="relative bg-[#fcfbf7] rounded-2xl p-6 flex flex-col min-h-[420px] overflow-hidden shadow-sm hover:shadow-lg transition-all duration-300 border border-neutral-100">
            <div class="flex justify-between mb-4">
              <div class="bg-gray-100 text-gray-700 text-xs font-medium px-2 py-1 rounded">
                {{ product.category }}
              </div>
              <div>
                <span v-if="product.stock"
                  class="inline-block text-green-700 bg-green-50 border border-green-200 text-xs px-3 py-0.5 rounded">
                  In Stock
                </span>
                <span v-else
                  class="inline-block text-red-700 bg-red-50 border border-red-200 text-xs px-3 py-0.5 rounded">
                  Out of Stock</span>
              </div>
            </div>
            <div>
              <h3 class="text-xl font-medium text-neutral-900 mb-2 z-10 relative">{{ product.name }}</h3>
              <p class="text-sm text-neutral-600 mb-2 leading-relaxed z-10 relative">{{ product.description }}</p>
            </div>
            <img :src="product.image" :alt="product.name"
              class="absolute right-0 -bottom-0 w-5/7 min-w-[120px] max-w-[330px] object-contain z-0 select-none pointer-events-none" />
            <div class="mt-auto flex flex-col sm:flex-row gap-3 z-10 relative">
              <button
                class="w-full bg-neutral-900 text-white px-5 py-2 rounded-full hover:bg-neutral-800 transition-all duration-200 font-medium cursor-pointer"
                :disabled="!product.stock" :class="{ 'opacity-60 cursor-not-allowed': !product.stock }">
                Get Started
              </button>
              <button
                class="w-full bg-white text-neutral-900 border border-neutral-300 px-5 py-2 rounded-full hover:bg-neutral-100 transition-all duration-200 font-medium cursor-pointer">
                Learn More
              </button>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Testimonials -->
    <section class="py-24 bg-white">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-20">
          <h2 class="text-4xl lg:text-5xl font-bold text-neutral-900 mb-6">
            Customer Stories
          </h2>
          <p class="text-xl text-neutral-600">
            Real experiences from our valued customers
          </p>
        </div>

        <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
          <div v-for="(testimonial, index) in testimonials" :key="testimonial.name"
            class="bg-neutral-50 p-8 rounded-3xl border border-neutral-200">
            <div class="flex items-center mb-6">
              <div v-for="i in 5" :key="i" class="text-yellow-400 mr-1">
                <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                  <path
                    d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z">
                  </path>
                </svg>
              </div>
            </div>
            <p class="text-neutral-700 mb-8 leading-relaxed text-lg">
              "{{ testimonial.review }}"
            </p>
            <div class="flex items-center">
              <img :src="`https://picsum.photos/48/48?random=${index + 20}`" :alt="testimonial.name"
                class="w-12 h-12 rounded-full mr-4" />
              <div>
                <p class="font-semibold text-neutral-900">
                  {{ testimonial.name }}
                </p>
                <p class="text-sm text-neutral-500">
                  {{ testimonial.location }}
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- FAQ Section -->
    <section class="py-20 bg-gradient-to-b from-white via-gray-50/30 to-gray-50">
      <div class="max-w-7xl mx-auto px-4">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-12">
          <div class="lg:sticky lg:top-32">
            <h2 class="text-4xl lg:text-5xl font-medium text-gray-900 mb-6">
              Frequently asked <span class="text-amber-500 italic">questions</span>
            </h2>
            <p class="text-lg text-gray-600 mb-8">
              Find answers to common questions about our medication subscriptions and medical review process.
            </p>
          </div>
          <div class="space-y-4">
            <div v-for="(faq, index) in faqs" :key="index" class="border border-gray-200 rounded-lg">
              <button @click="toggle(index)"
                class="w-full flex justify-between items-center p-6 text-left hover:bg-gray-50 transition-colors">
                <span class="font-semibold text-gray-900">
                  {{ faq.question }}
                </span>
                <svg class="w-6 h-6 text-gray-500 transition-transform duration-200" :class="{ 'rotate-45': faq.open }"
                  fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6">
                  </path>
                </svg>
              </button>
              <div class="overflow-hidden transition-all duration-300 ease-in-out"
                :class="{ 'max-h-96': faq.open, 'max-h-0': !faq.open }">
                <div class="px-6 pb-6 text-gray-600">
                  <p>{{ faq.answer }}</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- CTA Section -->
    <section class="relative py-28 bg-gradient-to-br from-amber-50 via-white to-amber-100 overflow-hidden">
      <!-- Animated background accent -->
      <div
        class="absolute -top-32 left-1/2 -translate-x-1/2 w-[700px] h-[700px] bg-amber-200/20 rounded-full blur-3xl animate-pulse-slow z-0">
      </div>
      <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center relative z-10">
        <h2 class="text-4xl lg:text-5xl font-extrabold text-neutral-900 mb-4 animate-fade-in">
          Take the First Step Toward a Healthier You
        </h2>
        <p class="text-2xl text-amber-600 font-semibold mb-4 animate-fade-in delay-100">
          Your journey to better health starts here.
        </p>
        <p class="text-xl text-neutral-700 mb-12 max-w-2xl mx-auto leading-relaxed animate-fade-in delay-200">
          Join thousands of satisfied customers who trust ConnectRx for their healthcare needs. Experience the
          difference with premium quality, expert support, and exceptional service.
        </p>
        <div class="flex flex-col sm:flex-row gap-6 justify-center animate-fade-in delay-300">
          <button
            class="bg-black text-white px-8 py-4 rounded-full text-lg font-semibold hover:bg-amber-500 hover:text-white transition-all duration-200 shadow-lg cursor-pointer">
            Explore Treatments
          </button>
        </div>
      </div>
    </section>

    <!-- Footer -->
    <footer
      class="bg-gradient-to-br from-neutral-900 via-neutral-800 to-neutral-900 border-t border-neutral-800/60 pt-20 pb-10 w-full">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-12 relative z-10 mb-12">
          <div>
            <h3 class="text-neutral-200 text-base font-semibold mb-4">Categories</h3>
            <ul class="space-y-3">
              <li><a href="#" class="text-neutral-300 hover:text-amber-400 transition-colors">Weight Loss</a></li>
              <li><a href="#" class="text-neutral-300 hover:text-amber-400 transition-colors">Sexual Health</a></li>
              <li><a href="#" class="text-neutral-300 hover:text-amber-400 transition-colors">Daily Wellness</a></li>
              <li><a href="#" class="text-neutral-300 hover:text-amber-400 transition-colors">Hair</a></li>
            </ul>
          </div>
          <div>
            <h3 class="text-neutral-200 text-base font-semibold mb-4">Follow us</h3>
            <ul class="space-y-3">
              <li class="flex items-center gap-2">
                <IconBrandInstagram class="w-5 h-5 text-neutral-300" /> <a href="#"
                  class="text-neutral-300 hover:text-amber-400 transition-colors">Instagram</a>
              </li>
              <li class="flex items-center gap-2">
                <IconBrandFacebook class="w-5 h-5 text-neutral-300" />
                <a href="#" class="text-neutral-300 hover:text-amber-400 transition-colors">Facebook</a>
              </li>
            </ul>
          </div>
          <div class="md:col-span-2">
            <h3 class="text-neutral-200 text-base font-semibold mb-4">Contact</h3>
            <div class="mb-2 text-neutral-400 text-sm">For support or questions about your order:</div>
            <a href="mailto:<EMAIL>"
              class="text-neutral-100 font-medium hover:text-amber-400 transition-colors inline-block mb-4"><EMAIL></a>
            <div class="mb-2 text-neutral-400 text-sm">For all other questions:</div>
            <a href="mailto:<EMAIL>"
              class="text-neutral-100 font-medium hover:text-amber-400 transition-colors inline-block"><EMAIL></a>
          </div>
        </div>
        <!-- Big Brand Name -->
        <div class="w-full flex flex-col items-center mb-12">
          <span
            class="text-[12vw] font-extrabold text-neutral-700/40 tracking-tighter text-center leading-none select-none">ConnectRx</span>
        </div>
        <div class="border-t border-neutral-700 pt-8 mt-8">
          <div class="flex flex-wrap justify-center items-center gap-8 mb-6">
            <img src="@/assets/images/hipaa-compliant.png" alt="HIPAA Compliant" class="h-12 w-auto object-contain" />
            <img src="@/assets/images/legit-script.png" alt="LegitScript Certified"
              class="h-12 w-auto object-contain" />
            <img src="@/assets/images/secure.png" alt="Secure & Encrypted" class="h-12 w-auto object-contain" />
          </div>
          <div class="flex flex-col md:flex-row justify-center items-center gap-4 text-neutral-500 text-xs">
            <a href="#" class="hover:text-amber-400 transition-colors">Terms &amp; Conditions</a>
            <span class="hidden md:inline">|</span>
            <a href="#" class="hover:text-amber-400 transition-colors">Privacy Policy</a>
            <span class="hidden md:inline">|</span>
            <span>&copy; 2024 ConnectRx. All rights reserved.</span>
          </div>
        </div>
      </div>
    </footer>
  </div>
</template>

<style scoped>
/* Slide down animation for mobile menu */
.slide-down-enter-active,
.slide-down-leave-active {
  transition: max-height 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.slide-down-enter-from,
.slide-down-leave-to {
  max-height: 0;
}

.slide-down-enter-to,
.slide-down-leave-from {
  max-height: 100vh;
}

.slide-up-enter-active,
.slide-up-leave-active {
  transition: all 200ms ease-in-out;
}

.slide-up-enter-from {
  transform: translateY(100%);
  opacity: 0;
}

.slide-up-enter-to {
  transform: translateY(0%);
  opacity: 1;
}

.slide-up-leave-from {
  transform: translateY(0%);
  opacity: 1;
}

.slide-up-leave-to {
  transform: translateY(-100%);
  opacity: 0;
}

/********* CTA Section Animations *********/
.animate-fade-in {
  animation: fadeInUp 0.7s cubic-bezier(0.4, 0, 0.2, 1) both;
}

.animate-fade-in.delay-100 {
  animation-delay: 0.1s;
}

.animate-fade-in.delay-200 {
  animation-delay: 0.2s;
}

.animate-fade-in.delay-300 {
  animation-delay: 0.3s;
}

.animate-pulse-slow {
  animation: pulseSlow 6s cubic-bezier(0.4, 0, 0.2, 1) infinite;
}

@keyframes fadeInUp {
  0% {
    opacity: 0;
    transform: translateY(40px);
  }

  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes pulseSlow {

  0%,
  100% {
    opacity: 0.7;
    transform: scale(1);
  }

  50% {
    opacity: 1;
    transform: scale(1.08);
  }
}
</style>
